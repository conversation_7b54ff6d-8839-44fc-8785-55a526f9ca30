import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import QuestionListingView, { Question } from '../../components/molecules/QuestionListingView/QuestionListingView';

// Mock the server actions
jest.mock('../../actions/worksheetQuestion.action', () => ({
  handleDeleteQuestionAction: jest.fn(),
  handleReorderQuestionsAction: jest.fn(),
}));

// Mock drag and drop
jest.mock('@hello-pangea/dnd', () => ({
  DragDropContext: ({ children }: any) => children,
  Droppable: ({ children }: any) => children({ innerRef: jest.fn(), droppableProps: {}, placeholder: null }, {}),
  Draggable: ({ children }: any) => children({ innerRef: jest.fn(), draggableProps: {}, dragHandleProps: {} }, {}),
}));

const mockQuestions: Question[] = [
  {
    type: 'multiple_choice',
    content: 'What is 2 + 2?',
    options: ['2', '3', '4', '5'],
    answer: ['4'],
    explain: 'Two plus two equals four.',
    subject: 'Mathematics'
  },
  {
    type: 'single_choice',
    content: 'The sky is blue.',
    options: ['True', 'False'],
    answer: ['True'],
    explain: 'The sky appears blue due to light scattering.',
    subject: 'Science'
  }
];

describe('QuestionListingView', () => {
  const defaultProps = {
    questions: mockQuestions,
    worksheetId: 'test-worksheet-id',
    enableDragAndDrop: false,
    enableDelete: false,
    isReadOnly: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders questions in view mode', () => {
    render(<QuestionListingView {...defaultProps} />);

    expect(screen.getByText('Question 1')).toBeInTheDocument();
    expect(screen.getByText('Question 2')).toBeInTheDocument();
    expect(screen.getByText('What is 2 + 2?')).toBeInTheDocument();
    expect(screen.getByText('The sky is blue.')).toBeInTheDocument();
  });

  it('shows drag handles when drag-and-drop is enabled', () => {
    render(<QuestionListingView {...defaultProps} enableDragAndDrop={true} />);

    // Should show drag handles (GripVertical icons)
    const dragHandles = screen.getAllByRole('generic');
    expect(dragHandles.length).toBeGreaterThan(0);
  });

  it('shows delete buttons when delete is enabled', () => {
    render(<QuestionListingView {...defaultProps} enableDelete={true} />);

    const deleteButtons = screen.getAllByRole('button');
    const deleteButtonsWithTrash = deleteButtons.filter(button => 
      button.querySelector('svg') // Looking for Trash2 icon
    );
    expect(deleteButtonsWithTrash.length).toBeGreaterThan(0);
  });

  it('hides drag handles and delete buttons in read-only mode', () => {
    render(<QuestionListingView {...defaultProps} enableDragAndDrop={true} enableDelete={true} isReadOnly={true} />);

    // Should not show delete buttons in read-only mode
    const deleteButtons = screen.queryAllByRole('button');
    expect(deleteButtons.length).toBe(0);
  });

  it('opens delete modal when delete button is clicked', async () => {
    const user = userEvent.setup();
    render(<QuestionListingView {...defaultProps} enableDelete={true} />);

    const deleteButtons = screen.getAllByRole('button');
    const deleteButton = deleteButtons.find(button => 
      button.querySelector('svg') // Looking for Trash2 icon
    );
    
    if (deleteButton) {
      await user.click(deleteButton);
      expect(screen.getByText('Delete Question')).toBeInTheDocument();
    }
  });

  it('calls onQuestionsChange when questions are updated', () => {
    const mockOnQuestionsChange = jest.fn();
    render(<QuestionListingView {...defaultProps} onQuestionsChange={mockOnQuestionsChange} />);

    // The component should call onQuestionsChange when questions change
    // This would typically happen through drag-and-drop or delete operations
    expect(mockOnQuestionsChange).not.toHaveBeenCalled();
  });

  it('displays question types correctly', () => {
    render(<QuestionListingView {...defaultProps} />);

    expect(screen.getByText('multiple choice')).toBeInTheDocument();
    expect(screen.getByText('single choice')).toBeInTheDocument();
  });

  it('displays question subjects', () => {
    render(<QuestionListingView {...defaultProps} />);

    expect(screen.getByText('Mathematics')).toBeInTheDocument();
    expect(screen.getByText('Science')).toBeInTheDocument();
  });

  it('shows explanation accordions', () => {
    render(<QuestionListingView {...defaultProps} />);

    const explanationButtons = screen.getAllByText('View Explanation');
    expect(explanationButtons).toHaveLength(2);
  });

  it('expands explanation when clicked', async () => {
    const user = userEvent.setup();
    render(<QuestionListingView {...defaultProps} />);

    const explanationButton = screen.getAllByText('View Explanation')[0];
    await user.click(explanationButton);

    expect(screen.getByText('Two plus two equals four.')).toBeInTheDocument();
  });
});
